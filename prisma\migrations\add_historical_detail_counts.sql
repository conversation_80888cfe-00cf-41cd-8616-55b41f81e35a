-- Migration: Add historical detail counts to academic_years table
-- This migration adds two new columns to store detailed historical counts

-- Add new columns for historical active and exited counts
ALTER TABLE `academic_years` 
ADD COLUMN `historical_active_count` INT DEFAULT 0,
ADD COLUMN `historical_exited_count` INT DEFAULT 0;

-- Update existing records to populate the new fields based on current_employee_count
-- For existing records, we'll assume all current_employee_count were active employees
UPDATE `academic_years` 
SET 
  `historical_active_count` = COALESCE(`current_employee_count`, 0),
  `historical_exited_count` = 0
WHERE `historical_active_count` IS NULL OR `historical_exited_count` IS NULL;

-- Add comments to document the new columns
ALTER TABLE `academic_years` 
MODIFY COLUMN `historical_active_count` INT DEFAULT 0 COMMENT 'Number of active employees when this academic year was created',
MODIFY COLUMN `historical_exited_count` INT DEFAULT 0 COMMENT 'Number of exited employees when this academic year was created';
