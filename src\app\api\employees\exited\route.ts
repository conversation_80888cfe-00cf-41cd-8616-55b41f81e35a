/**
 * API Route: GET /api/employees/exited
 *
 * Deskripsi: Mengambil daftar karyawan yang sudah di-softdelete
 * Penggunaan: Halaman daftar karyawan yang sudah keluar
 *
 * Response:
 * - 200: Daftar karyawan yang sudah di-softdelete
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin (bukan ADMIN)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';

export async function GET(_request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    // Query untuk mengambil karyawan yang sudah di-softdelete
    const exitedEmployees = await prisma.employee.findMany({
      where: {
        AND: [
          {
            // Exclude admin web and external members
            NOT: {
              OR: [
                { employeeId: { startsWith: "EMP" } }, // Mengecualikan admin web dan semua yang diawali EMP
                { employeeId: { startsWith: "EXT" } } // Mengecualikan anggota eksternal
              ]
            }
          },
          {
            isDeleted: true // Hanya ambil yang sudah di-softdelete
          }
        ]
      },
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        },
        position: {
          select: {
            id: true,
            title: true
          }
        },
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        academicYear: {
          select: {
            id: true,
            ta: true,
            description: true
          }
        }
      },
      orderBy: {
        deletedAt: 'desc' // Urutkan berdasarkan tanggal keluar terbaru
      }
    });

    logger.debug(`Fetched ${exitedEmployees.length} exited employees`);

    return NextResponse.json(exitedEmployees);
  } catch (error: any) {
    logger.error('API - Error fetching exited employees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch exited employees' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
